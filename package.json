{"name": "hospital-management-system", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "client:dev": "vite", "server:dev": "nodemon server/index.js", "server:only": "node server/index.js", "seed": "node server/utils/seedData.js", "build": "npm run build:client && npm run build:server", "build:client": "vite build", "build:server": "tsc -p server/tsconfig.json", "start": "node dist/server/index.js", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@types/react-router-dom": "^5.3.3", "bcryptjs": "^3.0.2", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^17.0.1", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.344.0", "mongodb": "^6.17.0", "mongoose": "^8.16.1", "morgan": "^1.10.0", "nodemailer": "^7.0.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.6.3", "socket.io": "^4.8.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "concurrently": "^9.2.0", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}