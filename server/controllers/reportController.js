import mongoose from 'mongoose';

// Get existing models
const getModels = () => {
  try {
    return {
      Patient: mongoose.model('Patient'),
      Appointment: mongoose.model('Appointment'),
      MedicalRecord: mongoose.model('MedicalRecord'),
      LabTest: mongoose.model('LabTest'),
      Bill: mongoose.model('Bill'),
      User: mongoose.model('User'),
      InventoryItem: mongoose.model('InventoryItem'),
      Prescription: mongoose.model('Prescription')
    };
  } catch (error) {
    console.error('Error getting models:', error);
    return {};
  }
};

// @desc    Get dashboard statistics
// @route   GET /api/reports/dashboard
// @access  Private
export const getDashboardStats = async (req, res) => {
  try {
    const models = getModels();
    const today = new Date();
    const startOfDay = new Date(today.setHours(0, 0, 0, 0));
    const endOfDay = new Date(today.setHours(23, 59, 59, 999));
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    const stats = await Promise.all([
      // Patient statistics
      models.Patient?.countDocuments({ isActive: true }) || 0,
      models.Patient?.countDocuments({ 
        createdAt: { $gte: startOfMonth },
        isActive: true 
      }) || 0,

      // Appointment statistics
      models.Appointment?.countDocuments({
        appointmentDate: { $gte: startOfDay, $lte: endOfDay }
      }) || 0,
      models.Appointment?.countDocuments({
        appointmentDate: { $gte: startOfDay, $lte: endOfDay },
        status: 'Scheduled'
      }) || 0,

      // Financial statistics
      models.Bill?.aggregate([
        {
          $match: {
            billDate: { $gte: startOfMonth },
            paymentStatus: { $ne: 'Cancelled' }
          }
        },
        {
          $group: {
            _id: null,
            totalRevenue: { $sum: '$totalAmount' },
            outstandingAmount: { $sum: '$balanceAmount' }
          }
        }
      ]) || [],

      // Lab test statistics
      models.LabTest?.countDocuments({
        status: { $in: ['Ordered', 'Sample Collected', 'In Progress'] }
      }) || 0,

      // Inventory alerts
      models.InventoryItem?.countDocuments({
        status: 'Active',
        $expr: { $lte: ['$currentStock', '$reorderLevel'] }
      }) || 0
    ]);

    const financialData = stats[4][0] || { totalRevenue: 0, outstandingAmount: 0 };

    res.status(200).json({
      success: true,
      data: {
        patients: {
          total: stats[0],
          newThisMonth: stats[1]
        },
        appointments: {
          today: stats[2],
          scheduled: stats[3]
        },
        financial: {
          monthlyRevenue: financialData.totalRevenue,
          outstandingAmount: financialData.outstandingAmount
        },
        laboratory: {
          pendingTests: stats[5]
        },
        inventory: {
          lowStockItems: stats[6]
        }
      }
    });
  } catch (error) {
    console.error('Get dashboard stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching dashboard statistics'
    });
  }
};

// @desc    Get patient analytics
// @route   GET /api/reports/patients
// @access  Private
export const getPatientAnalytics = async (req, res) => {
  try {
    const models = getModels();
    const { period = '30' } = req.query;
    const days = parseInt(period);
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const analytics = await Promise.all([
      // Patient registrations over time
      models.Patient?.aggregate([
        {
          $match: {
            createdAt: { $gte: startDate },
            isActive: true
          }
        },
        {
          $group: {
            _id: {
              year: { $year: '$createdAt' },
              month: { $month: '$createdAt' },
              day: { $dayOfMonth: '$createdAt' }
            },
            count: { $sum: 1 }
          }
        },
        { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
      ]) || [],

      // Gender distribution
      models.Patient?.aggregate([
        {
          $match: { isActive: true }
        },
        {
          $group: {
            _id: '$gender',
            count: { $sum: 1 }
          }
        }
      ]) || [],

      // Age distribution
      models.Patient?.aggregate([
        {
          $match: { isActive: true }
        },
        {
          $addFields: {
            age: {
              $floor: {
                $divide: [
                  { $subtract: [new Date(), '$dateOfBirth'] },
                  365.25 * 24 * 60 * 60 * 1000
                ]
              }
            }
          }
        },
        {
          $group: {
            _id: {
              $switch: {
                branches: [
                  { case: { $lt: ['$age', 18] }, then: '0-17' },
                  { case: { $lt: ['$age', 30] }, then: '18-29' },
                  { case: { $lt: ['$age', 50] }, then: '30-49' },
                  { case: { $lt: ['$age', 65] }, then: '50-64' }
                ],
                default: '65+'
              }
            },
            count: { $sum: 1 }
          }
        }
      ]) || []
    ]);

    res.status(200).json({
      success: true,
      data: {
        registrations: analytics[0],
        genderDistribution: analytics[1],
        ageDistribution: analytics[2]
      }
    });
  } catch (error) {
    console.error('Get patient analytics error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching patient analytics'
    });
  }
};

// @desc    Get appointment analytics
// @route   GET /api/reports/appointments
// @access  Private
export const getAppointmentAnalytics = async (req, res) => {
  try {
    const models = getModels();
    const { period = '30' } = req.query;
    const days = parseInt(period);
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const analytics = await Promise.all([
      // Appointments by status
      models.Appointment?.aggregate([
        {
          $match: {
            appointmentDate: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ]) || [],

      // Appointments by department
      models.Appointment?.aggregate([
        {
          $match: {
            appointmentDate: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: '$department',
            count: { $sum: 1 }
          }
        }
      ]) || [],

      // Daily appointment trends
      models.Appointment?.aggregate([
        {
          $match: {
            appointmentDate: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: {
              year: { $year: '$appointmentDate' },
              month: { $month: '$appointmentDate' },
              day: { $dayOfMonth: '$appointmentDate' }
            },
            count: { $sum: 1 }
          }
        },
        { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
      ]) || []
    ]);

    res.status(200).json({
      success: true,
      data: {
        statusDistribution: analytics[0],
        departmentDistribution: analytics[1],
        dailyTrends: analytics[2]
      }
    });
  } catch (error) {
    console.error('Get appointment analytics error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching appointment analytics'
    });
  }
};

// @desc    Get financial analytics
// @route   GET /api/reports/financial
// @access  Private
export const getFinancialAnalytics = async (req, res) => {
  try {
    const models = getModels();
    const { period = '30' } = req.query;
    const days = parseInt(period);
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const analytics = await Promise.all([
      // Revenue trends
      models.Bill?.aggregate([
        {
          $match: {
            billDate: { $gte: startDate },
            paymentStatus: { $ne: 'Cancelled' }
          }
        },
        {
          $group: {
            _id: {
              year: { $year: '$billDate' },
              month: { $month: '$billDate' },
              day: { $dayOfMonth: '$billDate' }
            },
            revenue: { $sum: '$totalAmount' },
            count: { $sum: 1 }
          }
        },
        { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
      ]) || [],

      // Payment status distribution
      models.Bill?.aggregate([
        {
          $match: {
            billDate: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: '$paymentStatus',
            count: { $sum: 1 },
            amount: { $sum: '$totalAmount' }
          }
        }
      ]) || [],

      // Revenue by category
      models.Bill?.aggregate([
        {
          $match: {
            billDate: { $gte: startDate },
            paymentStatus: { $ne: 'Cancelled' }
          }
        },
        { $unwind: '$items' },
        {
          $group: {
            _id: '$items.category',
            revenue: { $sum: '$items.totalPrice' },
            count: { $sum: 1 }
          }
        }
      ]) || []
    ]);

    res.status(200).json({
      success: true,
      data: {
        revenueTrends: analytics[0],
        paymentStatusDistribution: analytics[1],
        revenueByCategory: analytics[2]
      }
    });
  } catch (error) {
    console.error('Get financial analytics error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching financial analytics'
    });
  }
};

// @desc    Get laboratory analytics
// @route   GET /api/reports/laboratory
// @access  Private
export const getLaboratoryAnalytics = async (req, res) => {
  try {
    const models = getModels();
    const { period = '30' } = req.query;
    const days = parseInt(period);
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const analytics = await Promise.all([
      // Test volume by category
      models.LabTest?.aggregate([
        {
          $match: {
            orderDate: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: '$testCategory',
            count: { $sum: 1 }
          }
        }
      ]) || [],

      // Test status distribution
      models.LabTest?.aggregate([
        {
          $match: {
            orderDate: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ]) || [],

      // Daily test trends
      models.LabTest?.aggregate([
        {
          $match: {
            orderDate: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: {
              year: { $year: '$orderDate' },
              month: { $month: '$orderDate' },
              day: { $dayOfMonth: '$orderDate' }
            },
            count: { $sum: 1 }
          }
        },
        { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
      ]) || []
    ]);

    res.status(200).json({
      success: true,
      data: {
        testsByCategory: analytics[0],
        statusDistribution: analytics[1],
        dailyTrends: analytics[2]
      }
    });
  } catch (error) {
    console.error('Get laboratory analytics error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching laboratory analytics'
    });
  }
};
