import mongoose from 'mongoose';

// Inventory Item schema
const InventoryItemSchema = new mongoose.Schema({
  itemId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'INV' + Date.now().toString().slice(-6);
    }
  },
  name: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  description: {
    type: String,
    trim: true
  },
  category: {
    type: String,
    required: true,
    enum: ['Medication', 'Medical Equipment', 'Surgical Instruments', 'Consumables', 'Laboratory Supplies'],
    index: true
  },
  subcategory: {
    type: String,
    trim: true
  },
  manufacturer: {
    type: String,
    trim: true
  },
  batchNumber: {
    type: String,
    trim: true
  },
  expiryDate: {
    type: Date,
    index: true
  },
  unitOfMeasure: {
    type: String,
    required: true,
    trim: true
  },
  currentStock: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  minimumStock: {
    type: Number,
    required: true,
    min: 0,
    default: 10
  },
  maximumStock: {
    type: Number,
    required: true,
    min: 0
  },
  reorderLevel: {
    type: Number,
    required: true,
    min: 0
  },
  unitCost: {
    type: Number,
    required: true,
    min: 0
  },
  sellingPrice: {
    type: Number,
    min: 0
  },
  location: {
    building: String,
    floor: String,
    room: String,
    shelf: String
  },
  status: {
    type: String,
    enum: ['Active', 'Inactive', 'Expired', 'Recalled'],
    default: 'Active',
    index: true
  },
  isControlledSubstance: {
    type: Boolean,
    default: false
  },
  requiresPrescription: {
    type: Boolean,
    default: false
  },
  storageConditions: {
    type: String,
    trim: true
  },
  notes: {
    type: String,
    trim: true
  },
  lastUpdatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

// Prescription schema
const PrescriptionSchema = new mongoose.Schema({
  prescriptionId: {
    type: String,
    required: true,
    unique: true,
    default: function() {
      return 'RX' + Date.now().toString().slice(-6);
    }
  },
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: true
  },
  doctor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  appointment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Appointment'
  },
  medications: [{
    medication: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'InventoryItem',
      required: true
    },
    dosage: { type: String, required: true },
    frequency: { type: String, required: true },
    duration: { type: String, required: true },
    quantity: { type: Number, required: true, min: 1 },
    instructions: String,
    substitutionAllowed: { type: Boolean, default: true }
  }],
  status: {
    type: String,
    enum: ['Pending', 'Dispensed', 'Partially Dispensed', 'Cancelled', 'Expired'],
    default: 'Pending'
  },
  prescriptionDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  expiryDate: {
    type: Date,
    required: true,
    default: function() {
      const date = new Date();
      date.setDate(date.getDate() + 30); // 30 days validity
      return date;
    }
  },
  dispensedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  dispensedDate: Date,
  totalCost: {
    type: Number,
    min: 0
  },
  insurance: {
    covered: { type: Boolean, default: false },
    claimNumber: String,
    copayAmount: Number
  },
  notes: String
}, {
  timestamps: true
});

// Virtual for stock status
InventoryItemSchema.virtual('stockStatus').get(function() {
  if (this.currentStock <= 0) return 'Out of Stock';
  if (this.currentStock <= this.reorderLevel) return 'Low Stock';
  if (this.currentStock <= this.minimumStock) return 'Below Minimum';
  return 'In Stock';
});

// Virtual for days until expiry
InventoryItemSchema.virtual('daysUntilExpiry').get(function() {
  if (!this.expiryDate) return null;
  const today = new Date();
  const expiry = new Date(this.expiryDate);
  const diffTime = expiry.getTime() - today.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

// Indexes
InventoryItemSchema.index({ itemId: 1 });
InventoryItemSchema.index({ name: 'text', description: 'text' });
InventoryItemSchema.index({ category: 1, subcategory: 1 });
InventoryItemSchema.index({ currentStock: 1 });
InventoryItemSchema.index({ expiryDate: 1 });

PrescriptionSchema.index({ prescriptionId: 1 });
PrescriptionSchema.index({ patient: 1 });
PrescriptionSchema.index({ doctor: 1 });
PrescriptionSchema.index({ status: 1 });
PrescriptionSchema.index({ prescriptionDate: 1 });

// Get existing models or create new ones
let InventoryItem, Prescription;
try {
  InventoryItem = mongoose.model('InventoryItem');
  Prescription = mongoose.model('Prescription');
} catch (error) {
  InventoryItem = mongoose.model('InventoryItem', InventoryItemSchema);
  Prescription = mongoose.model('Prescription', PrescriptionSchema);
}

// @desc    Get all inventory items with pagination and filtering
// @route   GET /api/pharmacy/inventory
// @access  Private
export const getInventoryItems = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build filter object
    const filter = {};
    
    if (req.query.category) {
      filter.category = req.query.category;
    }
    
    if (req.query.status) {
      filter.status = req.query.status;
    }
    
    if (req.query.lowStock === 'true') {
      filter.$expr = { $lte: ['$currentStock', '$reorderLevel'] };
    }
    
    if (req.query.search) {
      filter.$text = { $search: req.query.search };
    }

    const items = await InventoryItem.find(filter)
      .populate('lastUpdatedBy', 'firstName lastName')
      .sort({ name: 1 })
      .skip(skip)
      .limit(limit);

    const total = await InventoryItem.countDocuments(filter);

    res.status(200).json({
      success: true,
      data: items,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get inventory items error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching inventory items'
    });
  }
};

// @desc    Get single inventory item
// @route   GET /api/pharmacy/inventory/:id
// @access  Private
export const getInventoryItem = async (req, res) => {
  try {
    const item = await InventoryItem.findById(req.params.id)
      .populate('lastUpdatedBy', 'firstName lastName email');

    if (!item) {
      return res.status(404).json({
        success: false,
        error: 'Inventory item not found'
      });
    }

    res.status(200).json({
      success: true,
      data: item
    });
  } catch (error) {
    console.error('Get inventory item error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching inventory item'
    });
  }
};

// @desc    Create new inventory item
// @route   POST /api/pharmacy/inventory
// @access  Private
export const createInventoryItem = async (req, res) => {
  try {
    req.body.lastUpdatedBy = req.user._id;

    const item = await InventoryItem.create(req.body);

    const populatedItem = await InventoryItem.findById(item._id)
      .populate('lastUpdatedBy', 'firstName lastName');

    res.status(201).json({
      success: true,
      data: populatedItem
    });
  } catch (error) {
    console.error('Create inventory item error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating inventory item'
    });
  }
};

// @desc    Update inventory item
// @route   PUT /api/pharmacy/inventory/:id
// @access  Private
export const updateInventoryItem = async (req, res) => {
  try {
    const item = await InventoryItem.findById(req.params.id);

    if (!item) {
      return res.status(404).json({
        success: false,
        error: 'Inventory item not found'
      });
    }

    req.body.lastUpdatedBy = req.user._id;

    const updatedItem = await InventoryItem.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    ).populate('lastUpdatedBy', 'firstName lastName');

    res.status(200).json({
      success: true,
      data: updatedItem
    });
  } catch (error) {
    console.error('Update inventory item error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while updating inventory item'
    });
  }
};

// @desc    Get all prescriptions with pagination and filtering
// @route   GET /api/pharmacy/prescriptions
// @access  Private
export const getPrescriptions = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build filter object
    const filter = {};
    
    if (req.query.status) {
      filter.status = req.query.status;
    }
    
    if (req.query.patient) {
      filter.patient = req.query.patient;
    }
    
    if (req.query.doctor) {
      filter.doctor = req.query.doctor;
    }

    const prescriptions = await Prescription.find(filter)
      .populate('patient', 'firstName lastName patientId phone')
      .populate('doctor', 'firstName lastName department')
      .populate('medications.medication', 'name unitOfMeasure sellingPrice')
      .populate('dispensedBy', 'firstName lastName')
      .sort({ prescriptionDate: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Prescription.countDocuments(filter);

    res.status(200).json({
      success: true,
      data: prescriptions,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get prescriptions error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching prescriptions'
    });
  }
};

// @desc    Create new prescription
// @route   POST /api/pharmacy/prescriptions
// @access  Private
export const createPrescription = async (req, res) => {
  try {
    const prescription = await Prescription.create(req.body);

    const populatedPrescription = await Prescription.findById(prescription._id)
      .populate('patient', 'firstName lastName patientId phone')
      .populate('doctor', 'firstName lastName department')
      .populate('medications.medication', 'name unitOfMeasure sellingPrice');

    res.status(201).json({
      success: true,
      data: populatedPrescription
    });
  } catch (error) {
    console.error('Create prescription error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating prescription'
    });
  }
};

// @desc    Get pharmacy statistics
// @route   GET /api/pharmacy/stats
// @access  Private
export const getPharmacyStats = async (req, res) => {
  try {
    const stats = await Promise.all([
      // Total inventory items
      InventoryItem.countDocuments({ status: 'Active' }),
      // Low stock items
      InventoryItem.countDocuments({
        status: 'Active',
        $expr: { $lte: ['$currentStock', '$reorderLevel'] }
      }),
      // Expired items
      InventoryItem.countDocuments({
        status: 'Active',
        expiryDate: { $lt: new Date() }
      }),
      // Pending prescriptions
      Prescription.countDocuments({ status: 'Pending' })
    ]);

    res.status(200).json({
      success: true,
      data: {
        totalItems: stats[0],
        lowStockItems: stats[1],
        expiredItems: stats[2],
        pendingPrescriptions: stats[3]
      }
    });
  } catch (error) {
    console.error('Get pharmacy stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching pharmacy statistics'
    });
  }
};
