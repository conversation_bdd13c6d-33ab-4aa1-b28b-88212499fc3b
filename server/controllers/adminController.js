import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import User from '../models/User.js';
import Role from '../models/Role.js';
import AuditLog from '../models/AuditLog.js';

// @desc    Get all users (Admin only)
// @route   GET /api/admin/users
// @access  Private/Admin
export const getAllUsers = async (req, res) => {
  try {
    const { page = 1, limit = 20, search, role, status, department } = req.query;

    // Build query
    const query = {};

    if (search) {
      query.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    if (role) query.role = role;
    if (status) query.status = status;
    if (department) query.department = department;

    const users = await User.find(query)
      .populate('role', 'name permissions')
      .select('-password')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await User.countDocuments(query);

    res.status(200).json({
      success: true,
      data: users,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get all users error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching users'
    });
  }
};

// @desc    Create new user (Admin only)
// @route   POST /api/admin/users
// @access  Private/Admin
export const createUser = async (req, res) => {
  try {
    const {
      firstName,
      lastName,
      email,
      password,
      role,
      department,
      phone,
      employeeId,
      position,
      specialization,
      licenseNumber
    } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        error: 'User with this email already exists'
      });
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Create user
    const user = await User.create({
      firstName,
      lastName,
      email,
      password: hashedPassword,
      role,
      department,
      phone,
      employeeId,
      position,
      specialization,
      licenseNumber,
      status: 'Active',
      createdBy: req.user.id
    });

    // Populate role information
    await user.populate('role', 'name permissions');

    // Remove password from response
    const userResponse = user.toObject();
    delete userResponse.password;

    res.status(201).json({
      success: true,
      data: userResponse
    });
  } catch (error) {
    console.error('Create user error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating user'
    });
  }
};

// @desc    Update user (Admin only)
// @route   PUT /api/admin/users/:id
// @access  Private/Admin
export const updateUser = async (req, res) => {
  try {
    const models = getModels();
    const { id } = req.params;
    const updateData = req.body;

    // Remove password from update data if present (use separate endpoint)
    delete updateData.password;

    const user = await models.User.findByIdAndUpdate(
      id,
      { ...updateData, updatedBy: req.user.id },
      { new: true, runValidators: true }
    ).populate('role', 'name permissions').select('-password');

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while updating user'
    });
  }
};

// @desc    Delete user (Admin only)
// @route   DELETE /api/admin/users/:id
// @access  Private/Admin
export const deleteUser = async (req, res) => {
  try {
    const models = getModels();
    const { id } = req.params;

    // Don't allow deletion of own account
    if (id === req.user.id) {
      return res.status(400).json({
        success: false,
        error: 'Cannot delete your own account'
      });
    }

    const user = await models.User.findByIdAndDelete(id);

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'User deleted successfully'
    });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while deleting user'
    });
  }
};

// @desc    Reset user password (Admin only)
// @route   PUT /api/admin/users/:id/reset-password
// @access  Private/Admin
export const resetUserPassword = async (req, res) => {
  try {
    const models = getModels();
    const { id } = req.params;
    const { newPassword } = req.body;

    if (!newPassword) {
      return res.status(400).json({
        success: false,
        error: 'New password is required'
      });
    }

    // Hash new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    const user = await models.User.findByIdAndUpdate(
      id,
      { 
        password: hashedPassword,
        passwordResetRequired: true,
        updatedBy: req.user.id
      },
      { new: true }
    ).select('-password');

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Password reset successfully'
    });
  } catch (error) {
    console.error('Reset password error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while resetting password'
    });
  }
};

// @desc    Toggle user status (Admin only)
// @route   PUT /api/admin/users/:id/toggle-status
// @access  Private/Admin
export const toggleUserStatus = async (req, res) => {
  try {
    const models = getModels();
    const { id } = req.params;

    // Don't allow deactivation of own account
    if (id === req.user.id) {
      return res.status(400).json({
        success: false,
        error: 'Cannot deactivate your own account'
      });
    }

    const user = await models.User.findById(id);

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    const newStatus = user.status === 'Active' ? 'Inactive' : 'Active';

    const updatedUser = await models.User.findByIdAndUpdate(
      id,
      { 
        status: newStatus,
        updatedBy: req.user.id
      },
      { new: true }
    ).populate('role', 'name permissions').select('-password');

    res.status(200).json({
      success: true,
      data: updatedUser
    });
  } catch (error) {
    console.error('Toggle user status error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while updating user status'
    });
  }
};

// @desc    Get user statistics (Admin only)
// @route   GET /api/admin/users/stats
// @access  Private/Admin
export const getUserStats = async (req, res) => {
  try {
    const models = getModels();

    const totalUsers = await models.User.countDocuments();
    const activeUsers = await models.User.countDocuments({ status: 'Active' });
    const inactiveUsers = await models.User.countDocuments({ status: 'Inactive' });
    
    // Get users by role
    const usersByRole = await models.User.aggregate([
      {
        $lookup: {
          from: 'roles',
          localField: 'role',
          foreignField: '_id',
          as: 'roleInfo'
        }
      },
      {
        $unwind: '$roleInfo'
      },
      {
        $group: {
          _id: '$roleInfo.name',
          count: { $sum: 1 }
        }
      }
    ]);

    // Get recent logins (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    const recentLogins = await models.User.countDocuments({
      lastLogin: { $gte: sevenDaysAgo }
    });

    res.status(200).json({
      success: true,
      data: {
        totalUsers,
        activeUsers,
        inactiveUsers,
        usersByRole,
        recentLogins
      }
    });
  } catch (error) {
    console.error('Get user stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching user statistics'
    });
  }
};

// @desc    Get all roles (Admin only)
// @route   GET /api/admin/roles
// @access  Private/Admin
export const getAllRoles = async (req, res) => {
  try {
    const models = getModels();

    const roles = await models.Role.find().sort({ name: 1 });

    res.status(200).json({
      success: true,
      data: roles
    });
  } catch (error) {
    console.error('Get all roles error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching roles'
    });
  }
};

// @desc    Create new role (Admin only)
// @route   POST /api/admin/roles
// @access  Private/Admin
export const createRole = async (req, res) => {
  try {
    const models = getModels();
    const { name, description, permissions } = req.body;

    // Check if role already exists
    const existingRole = await models.Role.findOne({ name });
    if (existingRole) {
      return res.status(400).json({
        success: false,
        error: 'Role with this name already exists'
      });
    }

    const role = await models.Role.create({
      name,
      description,
      permissions,
      createdBy: req.user.id
    });

    res.status(201).json({
      success: true,
      data: role
    });
  } catch (error) {
    console.error('Create role error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating role'
    });
  }
};

// @desc    Update role (Admin only)
// @route   PUT /api/admin/roles/:id
// @access  Private/Admin
export const updateRole = async (req, res) => {
  try {
    const models = getModels();
    const { id } = req.params;
    const { name, description, permissions } = req.body;

    const role = await models.Role.findByIdAndUpdate(
      id,
      {
        name,
        description,
        permissions,
        updatedBy: req.user.id
      },
      { new: true, runValidators: true }
    );

    if (!role) {
      return res.status(404).json({
        success: false,
        error: 'Role not found'
      });
    }

    res.status(200).json({
      success: true,
      data: role
    });
  } catch (error) {
    console.error('Update role error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while updating role'
    });
  }
};

// @desc    Delete role (Admin only)
// @route   DELETE /api/admin/roles/:id
// @access  Private/Admin
export const deleteRole = async (req, res) => {
  try {
    const models = getModels();
    const { id } = req.params;

    // Check if any users have this role
    const usersWithRole = await models.User.countDocuments({ role: id });
    if (usersWithRole > 0) {
      return res.status(400).json({
        success: false,
        error: `Cannot delete role. ${usersWithRole} users are assigned to this role.`
      });
    }

    const role = await models.Role.findByIdAndDelete(id);

    if (!role) {
      return res.status(404).json({
        success: false,
        error: 'Role not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Role deleted successfully'
    });
  } catch (error) {
    console.error('Delete role error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while deleting role'
    });
  }
};

// @desc    Get audit logs (Admin only)
// @route   GET /api/admin/audit-logs
// @access  Private/Admin
export const getAuditLogs = async (req, res) => {
  try {
    const models = getModels();
    const { page = 1, limit = 50, action, user, startDate, endDate } = req.query;

    // Build query
    const query = {};

    if (action) query.action = { $regex: action, $options: 'i' };
    if (user) query.user = user;

    if (startDate || endDate) {
      query.timestamp = {};
      if (startDate) query.timestamp.$gte = new Date(startDate);
      if (endDate) query.timestamp.$lte = new Date(endDate);
    }

    const auditLogs = await models.AuditLog.find(query)
      .populate('user', 'firstName lastName email')
      .sort({ timestamp: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await models.AuditLog.countDocuments(query);

    res.status(200).json({
      success: true,
      data: auditLogs,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get audit logs error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching audit logs'
    });
  }
};

// @desc    Create audit log entry
// @route   POST /api/admin/audit-logs
// @access  Private
export const createAuditLog = async (req, res) => {
  try {
    const models = getModels();
    const { action, resource, resourceId, details, ipAddress } = req.body;

    const auditLog = await models.AuditLog.create({
      user: req.user.id,
      action,
      resource,
      resourceId,
      details,
      ipAddress: ipAddress || req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date()
    });

    await auditLog.populate('user', 'firstName lastName email');

    res.status(201).json({
      success: true,
      data: auditLog
    });
  } catch (error) {
    console.error('Create audit log error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while creating audit log'
    });
  }
};

// @desc    Get system statistics (Admin only)
// @route   GET /api/admin/system-stats
// @access  Private/Admin
export const getSystemStats = async (req, res) => {
  try {
    const models = getModels();

    // Get various system statistics
    const totalPatients = await models.Patient.countDocuments();
    const totalAppointments = await models.Appointment.countDocuments();
    const totalUsers = await models.User.countDocuments();
    const totalRoles = await models.Role.countDocuments();

    // Get recent activity (last 24 hours)
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);

    const recentActivity = await models.AuditLog.countDocuments({
      timestamp: { $gte: yesterday }
    });

    // Get login statistics
    const activeUsers = await models.User.countDocuments({ status: 'Active' });
    const recentLogins = await models.User.countDocuments({
      lastLogin: { $gte: yesterday }
    });

    res.status(200).json({
      success: true,
      data: {
        totalPatients,
        totalAppointments,
        totalUsers,
        totalRoles,
        activeUsers,
        recentActivity,
        recentLogins,
        systemUptime: process.uptime(),
        timestamp: new Date()
      }
    });
  } catch (error) {
    console.error('Get system stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Server error while fetching system statistics'
    });
  }
};
