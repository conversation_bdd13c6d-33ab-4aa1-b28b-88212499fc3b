import express from 'express';
import {
  getAllUsers,
  createUser,
  updateUser,
  deleteUser,
  resetUserPassword,
  toggleUserStatus,
  getUserStats,
  getAllRoles,
  createRole,
  updateRole,
  deleteRole,
  getAuditLogs,
  createAuditLog,
  getSystemStats
} from '../controllers/adminController.js';
import { protect, restrictTo } from '../controllers/authController.js';

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Apply admin restriction to all routes
router.use(restrictTo('Administrator'));

// User management routes
router.route('/users')
  .get(getAllUsers)
  .post(createUser);

router.route('/users/:id')
  .put(updateUser)
  .delete(deleteUser);

router.put('/users/:id/reset-password', resetUserPassword);
router.put('/users/:id/toggle-status', toggleUserStatus);
router.get('/users/stats', getUserStats);

// Role management routes
router.route('/roles')
  .get(getAllRoles)
  .post(createRole);

router.route('/roles/:id')
  .put(updateRole)
  .delete(deleteRole);

// Audit log routes
router.route('/audit-logs')
  .get(getAuditLogs)
  .post(createAuditLog);

// System statistics
router.get('/system-stats', getSystemStats);

export default router;
