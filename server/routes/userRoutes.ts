import express from 'express';
import { body } from 'express-validator';
import {
  getUsers,
  getUser,
  createUser,
  updateUser,
  deleteUser,
  updateUserPermissions,
  getUsersByRole
} from '../controllers/userController.js';
import { protect, checkPermission, authorize } from '../middleware/authMiddleware.js';

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Routes
router.route('/')
  .get(checkPermission('admin', 'view', 'users'), getUsers)
  .post(checkPermission('admin', 'create', 'users'), createUser);

router.get('/by-role/:roleId', checkPermission('admin', 'view', 'users'), getUsersByRole);

router.route('/:id')
  .get(checkPermission('admin', 'view', 'users'), getUser)
  .put(checkPermission('admin', 'edit', 'users'), updateUser)
  .delete(checkPermission('admin', 'delete', 'users'), deleteUser);

router.put('/:id/permissions', checkPermission('admin', 'edit', 'users'), updateUserPermissions);

export default router;
