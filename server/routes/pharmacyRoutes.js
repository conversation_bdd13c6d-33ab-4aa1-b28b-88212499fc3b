import express from 'express';
import {
  getInventoryItems,
  getInventoryItem,
  createInventoryItem,
  updateInventoryItem,
  getPrescriptions,
  createPrescription,
  getPharmacyStats
} from '../controllers/pharmacyController.js';
import { protect } from '../controllers/authController.js';

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Inventory routes
router.route('/inventory')
  .get(getInventoryItems)
  .post(createInventoryItem);

router.route('/inventory/:id')
  .get(getInventoryItem)
  .put(updateInventoryItem);

// Prescription routes
router.route('/prescriptions')
  .get(getPrescriptions)
  .post(createPrescription);

// Statistics
router.get('/stats', getPharmacyStats);

export default router;
