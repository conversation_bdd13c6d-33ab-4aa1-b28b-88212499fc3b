import express from 'express';
import {
  getDashboardStats,
  getPatientAnalytics,
  getAppointmentAnalytics,
  getFinancialAnalytics,
  getLaboratoryAnalytics
} from '../controllers/reportController.js';
import { protect } from '../controllers/authController.js';

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// Dashboard and analytics routes
router.get('/dashboard', getDashboardStats);
router.get('/patients', getPatientAnalytics);
router.get('/appointments', getAppointmentAnalytics);
router.get('/financial', getFinancialAnalytics);
router.get('/laboratory', getLaboratoryAnalytics);

export default router;
