const API_BASE_URL = import.meta.env.VITE_REACT_APP_API_URL || 'http://localhost:3002/api';

// Generic API request function
const apiRequest = async (endpoint: string, options: RequestInit = {}) => {
  const token = localStorage.getItem('token');
  
  const config: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    },
    ...options,
  };

  const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'API request failed');
  }
  
  return response.json();
};

// Patient API
export const patientAPI = {
  getAll: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/patients${queryString}`);
  },
  getById: (id: string) => apiRequest(`/patients/${id}`),
  create: (data: any) => apiRequest('/patients', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  update: (id: string, data: any) => apiRequest(`/patients/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  delete: (id: string) => apiRequest(`/patients/${id}`, {
    method: 'DELETE',
  }),
};

// Appointment API
export const appointmentAPI = {
  getAll: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/appointments${queryString}`);
  },
  getById: (id: string) => apiRequest(`/appointments/${id}`),
  create: (data: any) => apiRequest('/appointments', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  update: (id: string, data: any) => apiRequest(`/appointments/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  delete: (id: string) => apiRequest(`/appointments/${id}`, {
    method: 'DELETE',
  }),
  getDoctorSchedule: (doctorId: string, date?: string) => {
    const params = date ? `?date=${date}` : '';
    return apiRequest(`/appointments/doctor/${doctorId}/schedule${params}`);
  },
  getAvailableSlots: (doctorId: string, date: string) => {
    return apiRequest(`/appointments/available-slots?doctorId=${doctorId}&date=${date}`);
  },
};

// Clinical API
export const clinicalAPI = {
  getMedicalRecords: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/clinical/medical-records${queryString}`);
  },
  getMedicalRecord: (id: string) => apiRequest(`/clinical/medical-records/${id}`),
  createMedicalRecord: (data: any) => apiRequest('/clinical/medical-records', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  updateMedicalRecord: (id: string, data: any) => apiRequest(`/clinical/medical-records/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  getPatientHistory: (patientId: string) => apiRequest(`/clinical/patients/${patientId}/history`),
};

// Laboratory API
export const laboratoryAPI = {
  getTests: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/laboratory/tests${queryString}`);
  },
  getTest: (id: string) => apiRequest(`/laboratory/tests/${id}`),
  createTest: (data: any) => apiRequest('/laboratory/tests', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  updateTest: (id: string, data: any) => apiRequest(`/laboratory/tests/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  getPatientTests: (patientId: string) => apiRequest(`/laboratory/patients/${patientId}/tests`),
  getStats: () => apiRequest('/laboratory/stats'),
};

// Pharmacy API
export const pharmacyAPI = {
  getInventory: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/pharmacy/inventory${queryString}`);
  },
  getInventoryItem: (id: string) => apiRequest(`/pharmacy/inventory/${id}`),
  createInventoryItem: (data: any) => apiRequest('/pharmacy/inventory', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  updateInventoryItem: (id: string, data: any) => apiRequest(`/pharmacy/inventory/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  getPrescriptions: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/pharmacy/prescriptions${queryString}`);
  },
  createPrescription: (data: any) => apiRequest('/pharmacy/prescriptions', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  getStats: () => apiRequest('/pharmacy/stats'),
};

// Financial API
export const financialAPI = {
  getBills: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/financial/bills${queryString}`);
  },
  getBill: (id: string) => apiRequest(`/financial/bills/${id}`),
  createBill: (data: any) => apiRequest('/financial/bills', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  updateBill: (id: string, data: any) => apiRequest(`/financial/bills/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  addPayment: (billId: string, data: any) => apiRequest(`/financial/bills/${billId}/payments`, {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  getStats: () => apiRequest('/financial/stats'),
};

// HR API
export const hrAPI = {
  getStaff: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/hr/staff${queryString}`);
  },
  getSchedules: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/hr/schedules${queryString}`);
  },
  createSchedule: (data: any) => apiRequest('/hr/schedules', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  getPerformance: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/hr/performance${queryString}`);
  },
  createPerformance: (data: any) => apiRequest('/hr/performance', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  getStats: () => apiRequest('/hr/stats'),
};

// Facility API
export const facilityAPI = {
  getRooms: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/facility/rooms${queryString}`);
  },
  createRoom: (data: any) => apiRequest('/facility/rooms', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  getEquipment: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/facility/equipment${queryString}`);
  },
  createEquipment: (data: any) => apiRequest('/facility/equipment', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  getMaintenanceRequests: (params?: Record<string, any>) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    return apiRequest(`/facility/maintenance${queryString}`);
  },
  createMaintenanceRequest: (data: any) => apiRequest('/facility/maintenance', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  getStats: () => apiRequest('/facility/stats'),
};

// Reports API
export const reportsAPI = {
  getDashboard: () => apiRequest('/reports/dashboard'),
  getPatientAnalytics: (period?: string) => {
    const params = period ? `?period=${period}` : '';
    return apiRequest(`/reports/patients${params}`);
  },
  getAppointmentAnalytics: (period?: string) => {
    const params = period ? `?period=${period}` : '';
    return apiRequest(`/reports/appointments${params}`);
  },
  getFinancialAnalytics: (period?: string) => {
    const params = period ? `?period=${period}` : '';
    return apiRequest(`/reports/financial${params}`);
  },
  getLaboratoryAnalytics: (period?: string) => {
    const params = period ? `?period=${period}` : '';
    return apiRequest(`/reports/laboratory${params}`);
  },
};

export default {
  patient: patientAPI,
  appointment: appointmentAPI,
  clinical: clinicalAPI,
  laboratory: laboratoryAPI,
  pharmacy: pharmacyAPI,
  financial: financialAPI,
  hr: hrAPI,
  facility: facilityAPI,
  reports: reportsAPI,
};
