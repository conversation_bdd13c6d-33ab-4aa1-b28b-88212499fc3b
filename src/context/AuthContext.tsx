import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { User, AuthState, ModulePermission } from '../types/auth';
import { authService } from '../services/authService';

interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  hasPermission: (module: string, action: string, resource?: string) => boolean;
  getModulePermissions: (module: string) => ModulePermission;
  updateUserPermissions: (userId: string, permissions: string[]) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

type AuthAction = 
  | { type: 'LOGIN_START' }
  | { type: 'LOGIN_SUCCESS'; payload: { user: User; token: string } }
  | { type: 'LOGIN_FAILURE' }
  | { type: 'LOGOUT' }
  | { type: 'UPDATE_USER'; payload: User };

const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'LOGIN_START':
      return { ...state, loading: true };
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        loading: false
      };
    case 'LOGIN_FAILURE':
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        loading: false
      };
    case 'LOGOUT':
      return {
        user: null,
        token: null,
        isAuthenticated: false,
        loading: false
      };
    case 'UPDATE_USER':
      return {
        ...state,
        user: action.payload
      };
    default:
      return state;
  }
};

const initialState: AuthState = {
  user: null,
  token: localStorage.getItem('token'),
  isAuthenticated: false,
  loading: false
};

export function AuthProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      authService.verifyToken(token)
        .then(user => {
          dispatch({ type: 'LOGIN_SUCCESS', payload: { user, token } });
        })
        .catch(() => {
          localStorage.removeItem('token');
          dispatch({ type: 'LOGOUT' });
        });
    }
  }, []);

  const login = async (email: string, password: string) => {
    console.log('AuthContext: Starting login process');
    dispatch({ type: 'LOGIN_START' });
    try {
      console.log('AuthContext: Calling authService.login');
      const { user, token } = await authService.login(email, password);
      console.log('AuthContext: Login successful, storing token and updating state');
      localStorage.setItem('token', token);
      dispatch({ type: 'LOGIN_SUCCESS', payload: { user, token } });
      console.log('AuthContext: Login process completed successfully');
    } catch (error) {
      console.error('AuthContext: Login failed', error);
      dispatch({ type: 'LOGIN_FAILURE' });
      throw error;
    }
  };

  const logout = () => {
    localStorage.removeItem('token');
    dispatch({ type: 'LOGOUT' });
  };

  const hasPermission = (module: string, action: string, resource?: string): boolean => {
    if (!state.user) return false;
    
    // Super admin has all permissions
    if (state.user.role.level === 10) return true;
    
    return state.user.permissions.some(permission => 
      permission.module === module && 
      permission.action === action &&
      (!resource || permission.resource === resource || permission.resource === '*')
    );
  };

  const getModulePermissions = (module: string): ModulePermission => {
    return {
      module,
      canView: hasPermission(module, 'view'),
      canCreate: hasPermission(module, 'create'),
      canEdit: hasPermission(module, 'edit'),
      canDelete: hasPermission(module, 'delete'),
      canExport: hasPermission(module, 'export'),
      canApprove: hasPermission(module, 'approve')
    };
  };

  const updateUserPermissions = async (userId: string, permissions: string[]) => {
    try {
      const updatedUser = await authService.updateUserPermissions(userId, permissions);
      if (state.user && state.user._id === userId) {
        dispatch({ type: 'UPDATE_USER', payload: updatedUser });
      }
    } catch (error) {
      throw error;
    }
  };

  return (
    <AuthContext.Provider value={{
      ...state,
      login,
      logout,
      hasPermission,
      getModulePermissions,
      updateUserPermissions
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};