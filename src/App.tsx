import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './context/AuthContext';
import { LoginForm } from './components/LoginForm';
import { Signup } from './components/Signup';
import { Dashboard } from './components/Dashboard';
import { PatientManagement } from './components/PatientManagement';
import { AppointmentManagement } from './components/AppointmentManagement';
import { ClinicalManagement } from './components/ClinicalManagement';
import { Laboratory } from './components/Laboratory';
import { Pharmacy } from './components/Pharmacy';
import { FinancialManagement } from './components/FinancialManagement';
import { HumanResources } from './components/HumanResources';
import { FacilityManagement } from './components/FacilityManagement';
import { Administration } from './components/Administration';
import { Reports } from './components/Reports';
import { UserManagement } from './components/UserManagement';
import { RoleBasedSidebar } from './components/RoleBasedSidebar';
import { Header } from './components/Header';
import { ProtectedRoute } from './components/ProtectedRoute';
import { LoadingScreen } from './components/LoadingScreen';

export type ActiveModule = 'dashboard' | 'patients' | 'clinical' | 'laboratory' | 'pharmacy' | 'financial' | 'hr' | 'facility' | 'admin' | 'reports' | 'users';

// Layout component for authenticated routes
function DashboardLayout({ children }: { children: React.ReactNode }) {
  const [sidebarOpen, setSidebarOpen] = useState(true);

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <RoleBasedSidebar
        isOpen={sidebarOpen}
        onToggle={() => setSidebarOpen(!sidebarOpen)}
      />

      <div className={`flex-1 transition-all duration-300 ${sidebarOpen ? 'ml-64' : 'ml-16'}`}>
        <Header onMenuToggle={() => setSidebarOpen(!sidebarOpen)} />
        <main className="p-6">
          {children}
        </main>
      </div>
    </div>
  );
}

function AppContent() {
  const { loading } = useAuth();

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <Routes>
      {/* Public routes */}
      <Route path="/login" element={<LoginForm />} />
      <Route path="/signup" element={<Signup />} />

      {/* Protected routes */}
      <Route path="/dashboard" element={
        <ProtectedRoute>
          <DashboardLayout>
            <Dashboard />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/patients" element={
        <ProtectedRoute>
          <DashboardLayout>
            <PatientManagement />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/appointments" element={
        <ProtectedRoute>
          <DashboardLayout>
            <AppointmentManagement />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/clinical" element={
        <ProtectedRoute>
          <DashboardLayout>
            <ClinicalManagement />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/laboratory" element={
        <ProtectedRoute>
          <DashboardLayout>
            <Laboratory />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/pharmacy" element={
        <ProtectedRoute>
          <DashboardLayout>
            <Pharmacy />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/financial" element={
        <ProtectedRoute>
          <DashboardLayout>
            <FinancialManagement />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/hr" element={
        <ProtectedRoute>
          <DashboardLayout>
            <HumanResources />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/facility" element={
        <ProtectedRoute>
          <DashboardLayout>
            <FacilityManagement />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/admin" element={
        <ProtectedRoute>
          <DashboardLayout>
            <Administration />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/reports" element={
        <ProtectedRoute>
          <DashboardLayout>
            <Reports />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/users" element={
        <ProtectedRoute>
          <DashboardLayout>
            <UserManagement />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      {/* Default redirect */}
      <Route path="/" element={<Navigate to="/dashboard" replace />} />
    </Routes>
  );
}

function App() {
  return (
    <Router>
      <AuthProvider>
        <AppContent />
      </AuthProvider>
    </Router>
  );
}

export default App;