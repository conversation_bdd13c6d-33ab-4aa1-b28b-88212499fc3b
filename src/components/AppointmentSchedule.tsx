import React from 'react';
import { Calendar, Clock, User } from 'lucide-react';

const appointments = [
  {
    id: 1,
    patient: '<PERSON>',
    doctor: 'Dr<PERSON> <PERSON>',
    time: '09:00 AM',
    type: 'Consultation',
    status: 'Confirmed'
  },
  {
    id: 2,
    patient: '<PERSON>',
    doctor: 'Dr<PERSON> <PERSON>',
    time: '10:30 AM',
    type: 'Follow-up',
    status: 'Pending'
  },
  {
    id: 3,
    patient: '<PERSON>',
    doctor: 'Dr<PERSON>',
    time: '02:00 PM',
    type: 'Surgery',
    status: 'Confirmed'
  },
  {
    id: 4,
    patient: '<PERSON>',
    doctor: 'Dr<PERSON> <PERSON>',
    time: '03:30 PM',
    type: 'Consultation',
    status: 'Rescheduled'
  }
];

export function AppointmentSchedule() {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Confirmed': return 'bg-green-100 text-green-800';
      case 'Pending': return 'bg-yellow-100 text-yellow-800';
      case 'Rescheduled': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-gray-900">Today's Schedule</h2>
        <Calendar size={20} className="text-gray-500" />
      </div>
      
      <div className="space-y-4">
        {appointments.map((appointment) => (
          <div key={appointment.id} className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <Clock size={16} className="text-gray-500" />
                <span className="text-sm font-medium text-gray-900">{appointment.time}</span>
              </div>
              <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(appointment.status)}`}>
                {appointment.status}
              </span>
            </div>
            
            <div className="space-y-1">
              <div className="flex items-center space-x-2">
                <User size={14} className="text-gray-400" />
                <span className="text-sm font-medium text-gray-900">{appointment.patient}</span>
              </div>
              <div className="text-sm text-gray-500">{appointment.doctor}</div>
              <div className="text-xs text-gray-400">{appointment.type}</div>
            </div>
          </div>
        ))}
      </div>
      
      <button className="w-full mt-4 text-sm text-blue-600 hover:text-blue-800 font-medium">
        View All Appointments
      </button>
    </div>
  );
}