import React, { useState } from 'react';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  DollarSign, 
  Calendar,
  Download,
  FileText,
  Activity,
  Target,
  AlertCircle
} from 'lucide-react';

const reportCategories = [
  {
    id: 'operational',
    title: 'Operational Reports',
    icon: Activity,
    color: 'bg-blue-500',
    reports: [
      { name: 'Patient Admission Report', frequency: 'Daily', lastGenerated: '2024-01-15' },
      { name: 'Bed Occupancy Report', frequency: 'Daily', lastGenerated: '2024-01-15' },
      { name: 'Staff Utilization Report', frequency: 'Weekly', lastGenerated: '2024-01-12' },
      { name: 'Equipment Usage Report', frequency: 'Monthly', lastGenerated: '2024-01-01' }
    ]
  },
  {
    id: 'clinical',
    title: 'Clinical Reports',
    icon: Target,
    color: 'bg-green-500',
    reports: [
      { name: 'Patient Outcome Report', frequency: 'Monthly', lastGenerated: '2024-01-01' },
      { name: 'Medication Error Report', frequency: 'Weekly', lastGenerated: '2024-01-12' },
      { name: 'Infection Control Report', frequency: 'Monthly', lastGenerated: '2024-01-01' },
      { name: 'Clinical Quality Indicators', frequency: 'Quarterly', lastGenerated: '2024-01-01' }
    ]
  },
  {
    id: 'financial',
    title: 'Financial Reports',
    icon: DollarSign,
    color: 'bg-purple-500',
    reports: [
      { name: 'Revenue Report', frequency: 'Monthly', lastGenerated: '2024-01-01' },
      { name: 'Cost Analysis Report', frequency: 'Monthly', lastGenerated: '2024-01-01' },
      { name: 'Insurance Claims Report', frequency: 'Weekly', lastGenerated: '2024-01-12' },
      { name: 'Accounts Receivable Report', frequency: 'Daily', lastGenerated: '2024-01-15' }
    ]
  },
  {
    id: 'compliance',
    title: 'Compliance Reports',
    icon: AlertCircle,
    color: 'bg-red-500',
    reports: [
      { name: 'Regulatory Compliance Report', frequency: 'Monthly', lastGenerated: '2024-01-01' },
      { name: 'Audit Trail Report', frequency: 'Daily', lastGenerated: '2024-01-15' },
      { name: 'Data Security Report', frequency: 'Monthly', lastGenerated: '2024-01-01' },
      { name: 'Policy Compliance Report', frequency: 'Quarterly', lastGenerated: '2024-01-01' }
    ]
  }
];

const kpiData = [
  {
    title: 'Patient Satisfaction',
    value: '94.2%',
    change: '+2.1%',
    changeType: 'increase',
    icon: Users,
    color: 'text-green-600'
  },
  {
    title: 'Average Length of Stay',
    value: '3.8 days',
    change: '-0.3 days',
    changeType: 'decrease',
    icon: Calendar,
    color: 'text-blue-600'
  },
  {
    title: 'Readmission Rate',
    value: '8.5%',
    change: '-1.2%',
    changeType: 'decrease',
    icon: TrendingUp,
    color: 'text-purple-600'
  },
  {
    title: 'Revenue Per Patient',
    value: '$4,250',
    change: '+5.8%',
    changeType: 'increase',
    icon: DollarSign,
    color: 'text-green-600'
  }
];

export function Reports() {
  const [activeCategory, setActiveCategory] = useState('operational');

  const selectedCategory = reportCategories.find(cat => cat.id === activeCategory);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Reports & Analytics</h1>
        <div className="flex space-x-3">
          <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
            <FileText size={20} />
            <span>Custom Report</span>
          </button>
          <button className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
            <Download size={20} />
            <span>Export Data</span>
          </button>
        </div>
      </div>

      {/* KPI Dashboard */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {kpiData.map((kpi) => {
          const Icon = kpi.icon;
          return (
            <div key={kpi.title} className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                    <Icon size={20} className={kpi.color} />
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">{kpi.title}</h3>
                    <p className="text-2xl font-bold text-gray-900">{kpi.value}</p>
                  </div>
                </div>
              </div>
              <div className={`text-sm ${kpi.changeType === 'increase' ? 'text-green-600' : 'text-red-600'}`}>
                {kpi.change} from last month
              </div>
            </div>
          );
        })}
      </div>

      {/* Analytics Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Chart Placeholder */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">Monthly Trends</h2>
            <Calendar size={20} className="text-gray-500" />
          </div>
          <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <BarChart3 size={48} className="text-gray-300 mx-auto mb-2" />
              <p className="text-gray-500">Interactive chart visualization</p>
            </div>
          </div>
        </div>

        {/* Performance Metrics */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">Performance Metrics</h2>
            <TrendingUp size={20} className="text-gray-500" />
          </div>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Bed Occupancy Rate</span>
              <div className="flex items-center space-x-2">
                <div className="w-24 bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-500 h-2 rounded-full" style={{ width: '87%' }}></div>
                </div>
                <span className="text-sm font-medium">87%</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Surgery Success Rate</span>
              <div className="flex items-center space-x-2">
                <div className="w-24 bg-gray-200 rounded-full h-2">
                  <div className="bg-green-500 h-2 rounded-full" style={{ width: '96%' }}></div>
                </div>
                <span className="text-sm font-medium">96%</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Staff Efficiency</span>
              <div className="flex items-center space-x-2">
                <div className="w-24 bg-gray-200 rounded-full h-2">
                  <div className="bg-purple-500 h-2 rounded-full" style={{ width: '82%' }}></div>
                </div>
                <span className="text-sm font-medium">82%</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Equipment Utilization</span>
              <div className="flex items-center space-x-2">
                <div className="w-24 bg-gray-200 rounded-full h-2">
                  <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '75%' }}></div>
                </div>
                <span className="text-sm font-medium">75%</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Report Categories */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {reportCategories.map((category) => (
              <button
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeCategory === category.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                {category.title}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {selectedCategory && (
            <div className="space-y-6">
              <div className="flex items-center space-x-3">
                <div className={`w-10 h-10 ${selectedCategory.color} rounded-lg flex items-center justify-center`}>
                  <selectedCategory.icon size={20} className="text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">{selectedCategory.title}</h2>
                  <p className="text-sm text-gray-500">Generate and manage {selectedCategory.title.toLowerCase()}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {selectedCategory.reports.map((report) => (
                  <div key={report.name} className="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900">{report.name}</h3>
                        <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                          <div className="flex items-center space-x-1">
                            <Calendar size={14} />
                            <span>{report.frequency}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <FileText size={14} />
                            <span>Last: {report.lastGenerated}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <button className="p-2 hover:bg-gray-200 rounded-lg transition-colors">
                          <FileText size={16} className="text-gray-500" />
                        </button>
                        <button className="p-2 hover:bg-gray-200 rounded-lg transition-colors">
                          <Download size={16} className="text-gray-500" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}