import React, { useState, useEffect, useRef } from 'react';
import { Menu, Bell, Search, User, Settings, LogOut, ChevronDown } from 'lucide-react';
import { useAuth } from '../context/AuthContext';

interface HeaderProps {
  onMenuToggle: () => void;
}

export function Header({ onMenuToggle }: HeaderProps) {
  const { user, logout } = useAuth();
  const [showUserMenu, setShowUserMenu] = useState(false);
  const userMenuRef = useRef<HTMLDivElement>(null);

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  return (
    <header className="bg-white/95 backdrop-blur-sm shadow-lg border-b border-slate-200 px-6 py-4 sticky top-0 z-40">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={onMenuToggle}
            className="p-2 hover:bg-slate-100 rounded-xl transition-colors"
          >
            <Menu size={20} className="text-slate-600" />
          </button>

          <div className="relative">
            <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
            <input
              type="text"
              placeholder="Search patients, staff, or records..."
              className="pl-10 pr-4 py-2.5 w-96 border border-slate-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-slate-50 focus:bg-white transition-colors text-sm"
            />
          </div>
        </div>

        <div className="flex items-center space-x-3">
          <button className="p-2.5 hover:bg-slate-100 rounded-xl transition-colors relative group">
            <Bell size={18} className="text-slate-600 group-hover:text-slate-800" />
            <span className="absolute -top-1 -right-1 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center shadow-lg animate-pulse">
              3
            </span>
          </button>

          <button className="p-2.5 hover:bg-slate-100 rounded-xl transition-colors group">
            <Settings size={18} className="text-slate-600 group-hover:text-slate-800" />
          </button>

          <div className="relative" ref={userMenuRef}>
            <button
              onClick={() => setShowUserMenu(!showUserMenu)}
              className="flex items-center space-x-3 pl-4 border-l border-slate-200 hover:bg-slate-50 rounded-xl p-2 transition-colors"
            >
              <div className="text-right">
                <div className="text-sm font-semibold text-slate-900">
                  {user?.firstName} {user?.lastName}
                </div>
                <div className="text-xs text-slate-500 font-medium">
                  {user?.role?.name || 'User'}
                </div>
              </div>
              <div className="w-9 h-9 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                <User size={16} className="text-white" />
              </div>
              <ChevronDown size={14} className={`text-slate-400 transition-transform ${showUserMenu ? 'rotate-180' : ''}`} />
            </button>

            {/* User Dropdown Menu */}
            {showUserMenu && (
              <div className="absolute right-0 mt-3 w-52 bg-white rounded-xl shadow-2xl border border-slate-200 py-2 z-50 backdrop-blur-sm">
                <div className="px-4 py-3 border-b border-slate-100">
                  <div className="text-sm font-semibold text-slate-900">
                    {user?.firstName} {user?.lastName}
                  </div>
                  <div className="text-xs text-slate-500 mt-1">{user?.email}</div>
                  <div className="text-xs text-blue-600 font-medium mt-1">{user?.role?.name}</div>
                </div>

                <button className="w-full text-left px-4 py-2.5 text-sm text-slate-700 hover:bg-slate-50 flex items-center transition-colors group">
                  <User size={16} className="mr-3 text-slate-400 group-hover:text-slate-600" />
                  <span className="font-medium">Profile</span>
                </button>

                <button className="w-full text-left px-4 py-2.5 text-sm text-slate-700 hover:bg-slate-50 flex items-center transition-colors group">
                  <Settings size={16} className="mr-3 text-slate-400 group-hover:text-slate-600" />
                  <span className="font-medium">Settings</span>
                </button>

                <div className="border-t border-slate-100 mt-2 pt-2">
                  <button
                    onClick={() => {
                      logout();
                      setShowUserMenu(false);
                    }}
                    className="w-full text-left px-4 py-2.5 text-sm text-red-600 hover:bg-red-50 flex items-center transition-colors group"
                  >
                    <LogOut size={16} className="mr-3 text-red-400 group-hover:text-red-600" />
                    <span className="font-medium">Sign Out</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}