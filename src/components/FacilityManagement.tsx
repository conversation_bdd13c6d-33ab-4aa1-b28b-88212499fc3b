import React, { useState } from 'react';
import { 
  Building, 
  Bed, 
  Wrench, 
  Calendar, 
  AlertCircle, 
  CheckCircle,
  Clock,
  MapPin,
  Settings
} from 'lucide-react';

const facilities = [
  {
    id: 'F001',
    name: 'Operation Theater 1',
    type: 'Operating Room',
    floor: '3rd Floor',
    capacity: 1,
    status: 'Available',
    equipment: ['Surgical Table', 'Anesthesia Machine', 'Monitors'],
    lastMaintenance: '2024-01-10',
    nextMaintenance: '2024-02-10'
  },
  {
    id: 'F002',
    name: 'ICU Ward',
    type: 'Intensive Care',
    floor: '2nd Floor',
    capacity: 20,
    status: 'Occupied',
    equipment: ['Ventilators', 'Monitors', 'Defibrillators'],
    lastMaintenance: '2024-01-05',
    nextMaintenance: '2024-02-05'
  },
  {
    id: 'F003',
    name: 'Emergency Department',
    type: 'Emergency',
    floor: 'Ground Floor',
    capacity: 15,
    status: 'Available',
    equipment: ['Trauma Beds', 'X-Ray Machine', 'Monitors'],
    lastMaintenance: '2024-01-12',
    nextMaintenance: '2024-02-12'
  },
  {
    id: 'F004',
    name: 'Laboratory',
    type: 'Diagnostic',
    floor: '1st Floor',
    capacity: 10,
    status: 'Under Maintenance',
    equipment: ['Microscopes', 'Centrifuge', 'Analyzers'],
    lastMaintenance: '2024-01-15',
    nextMaintenance: '2024-02-15'
  }
];

const maintenanceSchedule = [
  {
    id: 'M001',
    facilityId: 'F001',
    facilityName: 'Operation Theater 1',
    type: 'Preventive',
    description: 'Monthly equipment check and calibration',
    scheduledDate: '2024-02-10',
    technician: 'John Tech',
    status: 'Scheduled',
    priority: 'High'
  },
  {
    id: 'M002',
    facilityId: 'F002',
    facilityName: 'ICU Ward',
    type: 'Corrective',
    description: 'Ventilator repair and replacement',
    scheduledDate: '2024-01-18',
    technician: 'Sarah Mech',
    status: 'In Progress',
    priority: 'Critical'
  },
  {
    id: 'M003',
    facilityId: 'F003',
    facilityName: 'Emergency Department',
    type: 'Preventive',
    description: 'X-Ray machine maintenance',
    scheduledDate: '2024-02-12',
    technician: 'Mike Service',
    status: 'Scheduled',
    priority: 'Medium'
  }
];

const roomBookings = [
  {
    id: 'B001',
    facilityId: 'F001',
    facilityName: 'Operation Theater 1',
    patientName: 'John Doe',
    procedure: 'Cardiac Surgery',
    surgeon: 'Dr. Smith',
    date: '2024-01-16',
    startTime: '09:00 AM',
    endTime: '02:00 PM',
    status: 'Confirmed'
  },
  {
    id: 'B002',
    facilityId: 'F001',
    facilityName: 'Operation Theater 1',
    patientName: 'Jane Smith',
    procedure: 'Appendectomy',
    surgeon: 'Dr. Johnson',
    date: '2024-01-16',
    startTime: '03:00 PM',
    endTime: '05:00 PM',
    status: 'Confirmed'
  }
];

export function FacilityManagement() {
  const [activeTab, setActiveTab] = useState('facilities');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Available': return 'bg-green-100 text-green-800';
      case 'Occupied': return 'bg-yellow-100 text-yellow-800';
      case 'Under Maintenance': return 'bg-red-100 text-red-800';
      case 'Scheduled': return 'bg-blue-100 text-blue-800';
      case 'In Progress': return 'bg-orange-100 text-orange-800';
      case 'Completed': return 'bg-green-100 text-green-800';
      case 'Confirmed': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Critical': return 'bg-red-100 text-red-800';
      case 'High': return 'bg-orange-100 text-orange-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Facility Management</h1>
        <div className="flex space-x-3">
          <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
            <Calendar size={20} />
            <span>Schedule Maintenance</span>
          </button>
          <button className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
            <MapPin size={20} />
            <span>Book Room</span>
          </button>
        </div>
      </div>

      {/* Facility Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Facilities</p>
              <p className="text-2xl font-bold text-gray-900">{facilities.length}</p>
            </div>
            <Building size={24} className="text-blue-500" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Available</p>
              <p className="text-2xl font-bold text-gray-900">
                {facilities.filter(f => f.status === 'Available').length}
              </p>
            </div>
            <CheckCircle size={24} className="text-green-500" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Under Maintenance</p>
              <p className="text-2xl font-bold text-gray-900">
                {facilities.filter(f => f.status === 'Under Maintenance').length}
              </p>
            </div>
            <Wrench size={24} className="text-red-500" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Occupied</p>
              <p className="text-2xl font-bold text-gray-900">
                {facilities.filter(f => f.status === 'Occupied').length}
              </p>
            </div>
            <Bed size={24} className="text-yellow-500" />
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('facilities')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'facilities'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Facilities
            </button>
            <button
              onClick={() => setActiveTab('maintenance')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'maintenance'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Maintenance
            </button>
            <button
              onClick={() => setActiveTab('bookings')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'bookings'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Room Bookings
            </button>
            <button
              onClick={() => setActiveTab('assets')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'assets'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Asset Management
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'facilities' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-gray-900">Facility Overview</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {facilities.map((facility) => (
                  <div key={facility.id} className="bg-gray-50 rounded-lg p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                          <Building size={20} className="text-white" />
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900">{facility.name}</h3>
                          <p className="text-sm text-gray-500">{facility.type}</p>
                        </div>
                      </div>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(facility.status)}`}>
                        {facility.status}
                      </span>
                    </div>
                    
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center space-x-2">
                        <MapPin size={14} className="text-gray-400" />
                        <span className="text-gray-600">{facility.floor}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Bed size={14} className="text-gray-400" />
                        <span className="text-gray-600">Capacity: {facility.capacity}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Calendar size={14} className="text-gray-400" />
                        <span className="text-gray-600">Next Maintenance: {facility.nextMaintenance}</span>
                      </div>
                    </div>
                    
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <p className="text-sm text-gray-500 mb-2">Equipment:</p>
                      <div className="flex flex-wrap gap-1">
                        {facility.equipment.map((item, index) => (
                          <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                            {item}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'maintenance' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">Maintenance Schedule</h2>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <Wrench size={16} />
                  <span>Upcoming & Active</span>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Facility</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Type</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Description</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Scheduled Date</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Technician</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Priority</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Status</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {maintenanceSchedule.map((maintenance) => (
                      <tr key={maintenance.id} className="hover:bg-gray-50">
                        <td className="py-4 px-6 text-sm font-medium text-gray-900">{maintenance.facilityName}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{maintenance.type}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{maintenance.description}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{maintenance.scheduledDate}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{maintenance.technician}</td>
                        <td className="py-4 px-6">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(maintenance.priority)}`}>
                            {maintenance.priority}
                          </span>
                        </td>
                        <td className="py-4 px-6">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(maintenance.status)}`}>
                            {maintenance.status}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'bookings' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">Room Bookings</h2>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <Calendar size={16} />
                  <span>Today's Schedule</span>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Facility</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Patient</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Procedure</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Surgeon</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Date</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Time</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Status</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {roomBookings.map((booking) => (
                      <tr key={booking.id} className="hover:bg-gray-50">
                        <td className="py-4 px-6 text-sm font-medium text-gray-900">{booking.facilityName}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{booking.patientName}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{booking.procedure}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{booking.surgeon}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{booking.date}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{booking.startTime} - {booking.endTime}</td>
                        <td className="py-4 px-6">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(booking.status)}`}>
                            {booking.status}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'assets' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-gray-900">Asset Management</h2>
              
              <div className="text-center py-12">
                <Settings size={64} className="text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Asset Tracking</h3>
                <p className="text-gray-500">Track and manage hospital equipment and assets.</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}