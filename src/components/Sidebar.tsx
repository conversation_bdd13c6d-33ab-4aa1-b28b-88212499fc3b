import React from 'react';
import { 
  Home, 
  Users, 
  Stethoscope, 
  TestTube, 
  Pill, 
  DollarSign, 
  UserCheck, 
  Building, 
  Shield, 
  BarChart3,
  Heart,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { ActiveModule } from '../App';

interface SidebarProps {
  isOpen: boolean;
  activeModule: ActiveModule;
  onModuleChange: (module: ActiveModule) => void;
  onToggle: () => void;
}

const menuItems = [
  { id: 'dashboard', label: 'Dashboard', icon: Home },
  { id: 'patients', label: 'Patient Management', icon: Users },
  { id: 'clinical', label: 'Clinical Management', icon: Stethoscope },
  { id: 'laboratory', label: 'Laboratory', icon: TestTube },
  { id: 'pharmacy', label: 'Pharmacy', icon: Pill },
  { id: 'financial', label: 'Financial', icon: DollarSign },
  { id: 'hr', label: 'Human Resources', icon: UserCheck },
  { id: 'facility', label: 'Facility Management', icon: Building },
  { id: 'admin', label: 'Administration', icon: Shield },
  { id: 'reports', label: 'Reports & Analytics', icon: BarChart3 },
];

export function Sidebar({ isOpen, activeModule, onModuleChange, onToggle }: SidebarProps) {
  return (
    <div className={`fixed left-0 top-0 h-full bg-white shadow-lg transition-all duration-300 z-50 ${isOpen ? 'w-64' : 'w-16'}`}>
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className={`flex items-center space-x-3 ${isOpen ? '' : 'justify-center'}`}>
            <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
              <Heart size={20} className="text-white" />
            </div>
            {isOpen && (
              <div>
                <h1 className="text-xl font-bold text-gray-900">MediCore HMS</h1>
                <p className="text-xs text-gray-500">Hospital Management System</p>
              </div>
            )}
          </div>
          {isOpen && (
            <button
              onClick={onToggle}
              className="p-1 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ChevronLeft size={16} className="text-gray-600" />
            </button>
          )}
        </div>
      </div>

      <nav className="mt-6">
        <div className="px-2 space-y-1">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = activeModule === item.id;
            
            return (
              <button
                key={item.id}
                onClick={() => onModuleChange(item.id as ActiveModule)}
                className={`w-full flex items-center px-3 py-2 rounded-lg transition-colors ${
                  isActive 
                    ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600' 
                    : 'text-gray-700 hover:bg-gray-50'
                }`}
              >
                <Icon size={20} className={`${isActive ? 'text-blue-600' : 'text-gray-500'} ${isOpen ? 'mr-3' : ''}`} />
                {isOpen && <span className="font-medium">{item.label}</span>}
              </button>
            );
          })}
        </div>
      </nav>

      {!isOpen && (
        <div className="absolute bottom-4 left-2">
          <button
            onClick={onToggle}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ChevronRight size={16} className="text-gray-600" />
          </button>
        </div>
      )}
    </div>
  );
}