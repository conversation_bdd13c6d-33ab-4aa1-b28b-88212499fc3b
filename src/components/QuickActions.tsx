import React from 'react';
import { Plus, Calendar, FileText, Users, TestTube, Pill } from 'lucide-react';

const quickActions = [
  {
    id: 1,
    label: 'Add New Patient',
    icon: Plus,
    color: 'bg-blue-500 hover:bg-blue-600',
    action: () => console.log('Add new patient')
  },
  {
    id: 2,
    label: 'Schedule Appointment',
    icon: Calendar,
    color: 'bg-green-500 hover:bg-green-600',
    action: () => console.log('Schedule appointment')
  },
  {
    id: 3,
    label: 'Create Report',
    icon: FileText,
    color: 'bg-purple-500 hover:bg-purple-600',
    action: () => console.log('Create report')
  },
  {
    id: 4,
    label: 'View Staff',
    icon: Users,
    color: 'bg-orange-500 hover:bg-orange-600',
    action: () => console.log('View staff')
  },
  {
    id: 5,
    label: 'Lab Orders',
    icon: TestTube,
    color: 'bg-teal-500 hover:bg-teal-600',
    action: () => console.log('Lab orders')
  },
  {
    id: 6,
    label: 'Pharmacy',
    icon: Pill,
    color: 'bg-pink-500 hover:bg-pink-600',
    action: () => console.log('Pharmacy')
  }
];

export function QuickActions() {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
      
      <div className="grid grid-cols-2 gap-3">
        {quickActions.map((action) => {
          const Icon = action.icon;
          return (
            <button
              key={action.id}
              onClick={action.action}
              className={`p-4 rounded-lg text-white transition-colors ${action.color} group`}
            >
              <div className="flex flex-col items-center space-y-2">
                <Icon size={24} className="group-hover:scale-110 transition-transform" />
                <span className="text-sm font-medium text-center">{action.label}</span>
              </div>
            </button>
          );
        })}
      </div>
    </div>
  );
}