import React, { useState, useEffect } from 'react';
import {
  DollarSign,
  CreditCard,
  Receipt,
  TrendingUp,
  Calendar,
  FileText,
  AlertCircle,
  CheckCircle,
  Clock,
  Search,
  Filter,
  Plus,
  Eye,
  Edit,
  Loader
} from 'lucide-react';
import { financialAPI } from '../services/apiService';

interface Bill {
  _id: string;
  billId: string;
  patient: {
    _id: string;
    firstName: string;
    lastName: string;
    patientId: string;
  };
  billDate: string;
  dueDate: string;
  items: Array<{
    description: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    category: string;
  }>;
  subtotal: number;
  tax: number;
  discount: number;
  totalAmount: number;
  paidAmount: number;
  balanceAmount: number;
  paymentStatus: string;
  paymentMethod?: string;
  insurance?: {
    provider: string;
    policyNumber: string;
    claimAmount: number;
    claimStatus: string;
  };
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export function FinancialManagement() {
  const [bills, setBills] = useState<Bill[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedBill, setSelectedBill] = useState<Bill | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [stats, setStats] = useState<any>(null);

  // Fetch bills from API
  useEffect(() => {
    fetchBills();
    fetchStats();
  }, [currentPage, searchTerm, statusFilter]);

  const fetchBills = async () => {
    try {
      setLoading(true);
      const params: any = {
        page: currentPage,
        limit: 10
      };

      if (searchTerm) {
        params.search = searchTerm;
      }

      if (statusFilter !== 'all') {
        params.paymentStatus = statusFilter;
      }

      const response = await financialAPI.getBills(params);

      if (response.success) {
        setBills(response.data);
        setTotalPages(response.pagination?.pages || 1);
        setError(null);
      } else {
        setError('Failed to fetch bills');
      }
    } catch (err) {
      console.error('Error fetching bills:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch bills');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await financialAPI.getStats();
      if (response.success) {
        setStats(response.data);
      }
    } catch (err) {
      console.error('Error fetching financial stats:', err);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Paid': return 'bg-green-100 text-green-800';
      case 'Pending': return 'bg-yellow-100 text-yellow-800';
      case 'Overdue': return 'bg-red-100 text-red-800';
      case 'Approved': return 'bg-green-100 text-green-800';
      case 'Rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const totalRevenue = recentBills.reduce((sum, bill) => sum + bill.amount, 0);
  const pendingAmount = recentBills.filter(bill => bill.status === 'Pending').reduce((sum, bill) => sum + bill.amount, 0);
  const overdueAmount = recentBills.filter(bill => bill.status === 'Overdue').reduce((sum, bill) => sum + bill.amount, 0);
  const paidAmount = recentBills.filter(bill => bill.status === 'Paid').reduce((sum, bill) => sum + bill.amount, 0);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Financial Management</h1>
        <div className="flex space-x-3">
          <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
            <Receipt size={20} />
            <span>Create Invoice</span>
          </button>
          <button className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
            <FileText size={20} />
            <span>Generate Report</span>
          </button>
        </div>
      </div>

      {/* Financial Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900">${totalRevenue.toLocaleString()}</p>
              <p className="text-xs text-green-600 flex items-center mt-1">
                <TrendingUp size={12} className="mr-1" />
                +12% from last month
              </p>
            </div>
            <DollarSign size={24} className="text-green-500" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Pending Payments</p>
              <p className="text-2xl font-bold text-gray-900">${pendingAmount.toLocaleString()}</p>
              <p className="text-xs text-yellow-600 flex items-center mt-1">
                <Clock size={12} className="mr-1" />
                {recentBills.filter(bill => bill.status === 'Pending').length} invoices
              </p>
            </div>
            <Clock size={24} className="text-yellow-500" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Overdue Amount</p>
              <p className="text-2xl font-bold text-gray-900">${overdueAmount.toLocaleString()}</p>
              <p className="text-xs text-red-600 flex items-center mt-1">
                <AlertCircle size={12} className="mr-1" />
                {recentBills.filter(bill => bill.status === 'Overdue').length} overdue
              </p>
            </div>
            <AlertCircle size={24} className="text-red-500" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Payments Received</p>
              <p className="text-2xl font-bold text-gray-900">${paidAmount.toLocaleString()}</p>
              <p className="text-xs text-green-600 flex items-center mt-1">
                <CheckCircle size={12} className="mr-1" />
                This month
              </p>
            </div>
            <CheckCircle size={24} className="text-green-500" />
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('overview')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'overview'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Overview
            </button>
            <button
              onClick={() => setActiveTab('billing')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'billing'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Billing & Invoices
            </button>
            <button
              onClick={() => setActiveTab('insurance')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'insurance'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Insurance Claims
            </button>
            <button
              onClick={() => setActiveTab('reports')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'reports'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Financial Reports
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Revenue Chart Placeholder */}
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Monthly Revenue Trend</h3>
                  <div className="h-64 flex items-center justify-center">
                    <div className="text-center">
                      <TrendingUp size={48} className="text-gray-300 mx-auto mb-2" />
                      <p className="text-gray-500">Revenue chart visualization</p>
                    </div>
                  </div>
                </div>

                {/* Payment Methods */}
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Methods</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <CreditCard size={16} className="text-blue-500" />
                        <span className="text-sm text-gray-700">Credit Card</span>
                      </div>
                      <span className="text-sm font-medium text-gray-900">45%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Receipt size={16} className="text-green-500" />
                        <span className="text-sm text-gray-700">Cash</span>
                      </div>
                      <span className="text-sm font-medium text-gray-900">30%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <FileText size={16} className="text-purple-500" />
                        <span className="text-sm text-gray-700">Insurance</span>
                      </div>
                      <span className="text-sm font-medium text-gray-900">25%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'billing' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">Recent Invoices</h2>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <Calendar size={16} />
                  <span>Last 30 days</span>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Invoice ID</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Patient</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Date</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Amount</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Services</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Status</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Due Date</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {recentBills.map((bill) => (
                      <tr key={bill.id} className="hover:bg-gray-50">
                        <td className="py-4 px-6 text-sm font-medium text-gray-900">{bill.id}</td>
                        <td className="py-4 px-6">
                          <div className="font-medium text-gray-900">{bill.patientName}</div>
                          <div className="text-sm text-gray-500">{bill.patientId}</div>
                        </td>
                        <td className="py-4 px-6 text-sm text-gray-900">{bill.date}</td>
                        <td className="py-4 px-6 text-sm font-medium text-gray-900">
                          ${bill.amount.toLocaleString()}
                        </td>
                        <td className="py-4 px-6 text-sm text-gray-900">
                          {bill.services.join(', ')}
                        </td>
                        <td className="py-4 px-6">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(bill.status)}`}>
                            {bill.status}
                          </span>
                        </td>
                        <td className="py-4 px-6 text-sm text-gray-900">{bill.dueDate}</td>
                        <td className="py-4 px-6">
                          <div className="flex space-x-2">
                            <button className="text-blue-600 hover:text-blue-800 text-sm">View</button>
                            <button className="text-green-600 hover:text-green-800 text-sm">Payment</button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'insurance' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">Insurance Claims</h2>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <FileText size={16} />
                  <span>Claims management</span>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Claim ID</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Patient</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Insurance Provider</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Claim Amount</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Approved Amount</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Status</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Submission Date</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {insuranceClaims.map((claim) => (
                      <tr key={claim.id} className="hover:bg-gray-50">
                        <td className="py-4 px-6 text-sm font-medium text-gray-900">{claim.id}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{claim.patientName}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{claim.insuranceProvider}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">
                          ${claim.claimAmount.toLocaleString()}
                        </td>
                        <td className="py-4 px-6 text-sm text-gray-900">
                          {claim.approvedAmount > 0 ? `$${claim.approvedAmount.toLocaleString()}` : '-'}
                        </td>
                        <td className="py-4 px-6">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(claim.status)}`}>
                            {claim.status}
                          </span>
                        </td>
                        <td className="py-4 px-6 text-sm text-gray-900">{claim.submissionDate}</td>
                        <td className="py-4 px-6">
                          <div className="flex space-x-2">
                            <button className="text-blue-600 hover:text-blue-800 text-sm">View</button>
                            <button className="text-green-600 hover:text-green-800 text-sm">Resubmit</button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'reports' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-gray-900">Financial Reports</h2>
              
              <div className="text-center py-12">
                <BarChart3 size={64} className="text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Financial Analytics</h3>
                <p className="text-gray-500">Generate detailed financial reports and analytics.</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}