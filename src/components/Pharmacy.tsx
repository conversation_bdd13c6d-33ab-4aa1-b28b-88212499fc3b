import React, { useState, useEffect } from 'react';
import {
  Pill,
  Search,
  Filter,
  AlertTriangle,
  Package,
  Plus,
  ShoppingCart,
  TrendingDown,
  Calendar,
  Loader,
  Eye,
  Edit
} from 'lucide-react';
import { pharmacyAPI } from '../services/apiService';

interface InventoryItem {
  _id: string;
  itemId: string;
  name: string;
  genericName: string;
  strength: string;
  dosageForm: string;
  category: string;
  manufacturer: string;
  supplier: string;
  batchNumber: string;
  expiryDate: string;
  currentStock: number;
  reorderLevel: number;
  unitPrice: number;
  totalValue: number;
  location: string;
  status: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

interface Prescription {
  _id: string;
  prescriptionId: string;
  patient: {
    _id: string;
    firstName: string;
    lastName: string;
    patientId: string;
  };
  doctor: {
    _id: string;
    firstName: string;
    lastName: string;
  };
  medications: Array<{
    medication: string;
    dosage: string;
    frequency: string;
    duration: string;
    instructions: string;
  }>;
  prescriptionDate: string;
  status: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export function Pharmacy() {
  const [activeTab, setActiveTab] = useState('inventory');
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [prescriptions, setPrescriptions] = useState<Prescription[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null);
  const [selectedPrescription, setSelectedPrescription] = useState<Prescription | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Fetch data based on active tab
  useEffect(() => {
    if (activeTab === 'inventory') {
      fetchInventoryItems();
    } else if (activeTab === 'prescriptions') {
      fetchPrescriptions();
    }
  }, [activeTab, currentPage, searchTerm, statusFilter]);

  const fetchInventoryItems = async () => {
    try {
      setLoading(true);
      const params: any = {
        page: currentPage,
        limit: 10
      };

      if (searchTerm) {
        params.search = searchTerm;
      }

      if (statusFilter !== 'all') {
        params.status = statusFilter;
      }

      const response = await pharmacyAPI.getInventory(params);

      if (response.success) {
        setInventoryItems(response.data);
        setTotalPages(response.pagination?.pages || 1);
        setError(null);
      } else {
        setError('Failed to fetch inventory items');
      }
    } catch (err) {
      console.error('Error fetching inventory items:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch inventory items');
    } finally {
      setLoading(false);
    }
  };

  const fetchPrescriptions = async () => {
    try {
      setLoading(true);
      const params: any = {
        page: currentPage,
        limit: 10
      };

      if (searchTerm) {
        params.search = searchTerm;
      }

      if (statusFilter !== 'all') {
        params.status = statusFilter;
      }

      const response = await pharmacyAPI.getPrescriptions(params);

      if (response.success) {
        setPrescriptions(response.data);
        setTotalPages(response.pagination?.pages || 1);
        setError(null);
      } else {
        setError('Failed to fetch prescriptions');
      }
    } catch (err) {
      console.error('Error fetching prescriptions:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch prescriptions');
    } finally {
      setLoading(false);
    }
  };

  const getStockStatus = (currentStock: number, reorderLevel: number) => {
    if (currentStock === 0) return { status: 'Out of Stock', color: 'bg-red-100 text-red-800' };
    if (currentStock <= reorderLevel) return { status: 'Low Stock', color: 'bg-yellow-100 text-yellow-800' };
    return { status: 'In Stock', color: 'bg-green-100 text-green-800' };
  };

  const getExpiryStatus = (expiryDate: string) => {
    const today = new Date();
    const expiry = new Date(expiryDate);
    const daysUntilExpiry = Math.ceil((expiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

    if (daysUntilExpiry < 0) return { status: 'Expired', color: 'bg-red-100 text-red-800' };
    if (daysUntilExpiry <= 30) return { status: 'Expiring Soon', color: 'bg-yellow-100 text-yellow-800' };
    return { status: 'Valid', color: 'bg-green-100 text-green-800' };
  };

  const getPrescriptionStatus = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'dispensed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Pharmacy Management</h1>
        <div className="flex space-x-3">
          <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
            <Plus size={20} />
            <span>Add Medication</span>
          </button>
          <button className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
            <ShoppingCart size={20} />
            <span>New Order</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Medications</p>
              <p className="text-2xl font-bold text-gray-900">{medications.length}</p>
            </div>
            <Pill size={24} className="text-blue-500" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Low Stock Items</p>
              <p className="text-2xl font-bold text-gray-900">{getLowStockMeds().length}</p>
            </div>
            <TrendingDown size={24} className="text-red-500" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Expiring Soon</p>
              <p className="text-2xl font-bold text-gray-900">{getExpiringMeds().length}</p>
            </div>
            <Calendar size={24} className="text-yellow-500" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Value</p>
              <p className="text-2xl font-bold text-gray-900">$12,450</p>
            </div>
            <Package size={24} className="text-green-500" />
          </div>
        </div>
      </div>

      {/* Alerts */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Alerts</h2>
          <AlertTriangle size={20} className="text-orange-500" />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <AlertTriangle size={16} className="text-red-500" />
              <span className="text-sm font-medium text-red-800">Low Stock Alert</span>
            </div>
            <p className="text-sm text-red-700">
              {getLowStockMeds().length} medications are running low on stock
            </p>
          </div>
          
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Calendar size={16} className="text-yellow-500" />
              <span className="text-sm font-medium text-yellow-800">Expiry Alert</span>
            </div>
            <p className="text-sm text-yellow-700">
              {getExpiringMeds().length} medications expiring within 3 months
            </p>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('inventory')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'inventory'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Inventory
            </button>
            <button
              onClick={() => setActiveTab('prescriptions')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'prescriptions'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Prescriptions
            </button>
            <button
              onClick={() => setActiveTab('orders')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'orders'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Purchase Orders
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'inventory' && (
            <div className="space-y-6">
              {/* Search and Filter */}
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1 relative">
                  <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search medications..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div className="flex items-center space-x-3">
                  <Filter size={20} className="text-gray-400" />
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="all">All Status</option>
                    <option value="in stock">In Stock</option>
                    <option value="low stock">Low Stock</option>
                    <option value="out of stock">Out of Stock</option>
                  </select>
                </div>
              </div>

              {/* Medications Table */}
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Medication</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Category</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Stock</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Expiry</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Price</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Status</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {filteredMedications.map((med) => (
                      <tr key={med.id} className="hover:bg-gray-50">
                        <td className="py-4 px-6">
                          <div>
                            <div className="font-medium text-gray-900">{med.name}</div>
                            <div className="text-sm text-gray-500">{med.generic} - {med.strength} {med.form}</div>
                          </div>
                        </td>
                        <td className="py-4 px-6 text-sm text-gray-900">{med.category}</td>
                        <td className="py-4 px-6">
                          <div className="text-sm text-gray-900">{med.stock}</div>
                          <div className="text-xs text-gray-500">Min: {med.minStock}</div>
                        </td>
                        <td className="py-4 px-6 text-sm text-gray-900">{med.expiry}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">${med.price}</td>
                        <td className="py-4 px-6">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(med.status)}`}>
                            {med.status}
                          </span>
                        </td>
                        <td className="py-4 px-6">
                          <div className="flex space-x-2">
                            <button className="text-blue-600 hover:text-blue-800 text-sm">Edit</button>
                            <button className="text-green-600 hover:text-green-800 text-sm">Reorder</button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'prescriptions' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-gray-900">Prescription Management</h2>
              
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Prescription ID</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Patient</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Doctor</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Date</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Medications</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Status</th>
                      <th className="text-left py-3 px-6 text-sm font-medium text-gray-500">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {prescriptions.map((prescription) => (
                      <tr key={prescription.id} className="hover:bg-gray-50">
                        <td className="py-4 px-6 text-sm font-medium text-gray-900">{prescription.id}</td>
                        <td className="py-4 px-6">
                          <div className="font-medium text-gray-900">{prescription.patientName}</div>
                          <div className="text-sm text-gray-500">{prescription.patientId}</div>
                        </td>
                        <td className="py-4 px-6 text-sm text-gray-900">{prescription.doctor}</td>
                        <td className="py-4 px-6 text-sm text-gray-900">{prescription.date}</td>
                        <td className="py-4 px-6">
                          <div className="text-sm text-gray-900">
                            {prescription.medications.length} medication(s)
                          </div>
                        </td>
                        <td className="py-4 px-6">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(prescription.status)}`}>
                            {prescription.status}
                          </span>
                        </td>
                        <td className="py-4 px-6">
                          <div className="flex space-x-2">
                            <button className="text-blue-600 hover:text-blue-800 text-sm">View</button>
                            <button className="text-green-600 hover:text-green-800 text-sm">Dispense</button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'orders' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-gray-900">Purchase Orders</h2>
              
              <div className="text-center py-12">
                <ShoppingCart size={64} className="text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Purchase Order Management</h3>
                <p className="text-gray-500">Create and manage purchase orders for medication restocking.</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}